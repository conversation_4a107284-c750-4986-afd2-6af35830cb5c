"use client";

import React from "react";
import { useQuery } from "@tanstack/react-query";
import { 
  AlertCircle, 
  TrendingUp, 
  Clock, 
  Target,
  Calendar,
  DollarSign,
  Users,
  CheckCircle,
  ArrowRight,
  Zap
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useTRPC } from "@/components/trpc/client";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface SmartQuickActionsProps {
  userRole: string;
  onQuickAction: (action: string) => void;
}

export function SmartQuickActions({ userRole, onQuickAction }: SmartQuickActionsProps) {
  const trpc = useTRPC();
  const pathname = usePathname();

  // Fetch user-specific data based on role
  const { data: accountStats, isLoading: accountStatsLoading } = useQuery(
    trpc.accounts.getStats.queryOptions(undefined, {
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  );

  const { data: contractorStats, isLoading: contractorStatsLoading } = useQuery({
    ...trpc.contractor.getStats.queryOptions(),
    enabled: userRole === 'contractor',
    staleTime: 5 * 60 * 1000,
  });

  const { data: pendingBids, isLoading: pendingBidsLoading } = useQuery({
    ...trpc.dashboard.getPendingBidsForUser.queryOptions(),
    enabled: userRole === 'homeowner',
    staleTime: 2 * 60 * 1000, // 2 minutes for more real-time data
  });

  // Generate smart actions based on real data
  const getSmartActions = () => {
    const actions = [];
    
    if (userRole === 'homeowner') {
      // Pending bids actions
      if (pendingBids && pendingBids.length > 0) {
        actions.push({
          id: 'review-pending-bids',
          label: `Review ${pendingBids.length} pending bid${pendingBids.length > 1 ? 's' : ''}`,
          description: `You have ${pendingBids.length} contractor${pendingBids.length > 1 ? 's' : ''} waiting for your response`,
          icon: <Users className="h-4 w-4" />,
          urgency: 'high',
          action: `I have ${pendingBids.length} pending bids that need my attention. Can you help me review and compare them?`,
          badge: pendingBids.length.toString(),
          badgeColor: 'bg-tradecrews-orange text-white'
        });
      }

      // Active projects
      if (accountStats?.activeJobs && accountStats.activeJobs > 0) {
        actions.push({
          id: 'manage-active-projects',
          label: `Manage ${accountStats.activeJobs} active project${accountStats.activeJobs > 1 ? 's' : ''}`,
          description: 'Check status and progress of your active projects',
          icon: <Target className="h-4 w-4" />,
          urgency: 'medium',
          action: `I have ${accountStats.activeJobs} active project${accountStats.activeJobs > 1 ? 's' : ''}. Can you help me review their status and next steps?`,
          badge: accountStats.activeJobs.toString(),
          badgeColor: 'bg-tradecrews-blue text-white'
        });
      }

      // Completed projects for potential reviews
      if (accountStats?.completedJobs && accountStats.completedJobs > 0) {
        actions.push({
          id: 'review-completed',
          label: `Review ${accountStats.completedJobs} completed project${accountStats.completedJobs > 1 ? 's' : ''}`,
          description: 'Leave reviews for completed work',
          icon: <CheckCircle className="h-4 w-4" />,
          urgency: 'low',
          action: `I have ${accountStats.completedJobs} completed project${accountStats.completedJobs > 1 ? 's' : ''}. Can you help me leave reviews for the contractors?`,
          badge: accountStats.completedJobs.toString(),
          badgeColor: 'bg-green-500 text-white'
        });
      }

      // Context-specific actions
      if (pathname.includes('/projects/') && !pathname.includes('/new')) {
        actions.push({
          id: 'optimize-current-project',
          label: 'Optimize this project',
          description: 'Get suggestions to improve this project',
          icon: <Zap className="h-4 w-4" />,
          urgency: 'low',
          action: 'Analyze this project and give me suggestions to optimize the timeline, budget, or requirements.',
          badge: null,
          badgeColor: ''
        });
      }
    }

    if (userRole === 'contractor') {
      // Bid performance insights
      if (contractorStats?.totalBids && contractorStats.totalBids >= 5) {
        // Calculate acceptance rate from available data
        const acceptedBids = contractorStats.completedJobs || 0;
        const acceptanceRate = (acceptedBids / contractorStats.totalBids) * 100;
        
        if (acceptanceRate < 30) {
          actions.push({
            id: 'improve-bid-strategy',
            label: 'Improve bid strategy',
            description: `${acceptanceRate.toFixed(0)}% success rate - let's optimize`,
            icon: <TrendingUp className="h-4 w-4" />,
            urgency: 'high',
            action: `My bid success rate is ${acceptanceRate.toFixed(0)}%. Can you analyze my bidding patterns and suggest improvements?`,
            badge: `${acceptanceRate.toFixed(0)}%`,
            badgeColor: 'bg-red-500 text-white'
          });
        }
      }

      // Active jobs for contractors
      if (contractorStats?.activeJobs && contractorStats.activeJobs > 0) {
        actions.push({
          id: 'manage-active-work',
          label: `Manage ${contractorStats.activeJobs} active job${contractorStats.activeJobs > 1 ? 's' : ''}`,
          description: 'Track progress on your current projects',
          icon: <Clock className="h-4 w-4" />,
          urgency: 'medium',
          action: `I have ${contractorStats.activeJobs} active job${contractorStats.activeJobs > 1 ? 's' : ''}. Can you help me track progress and manage timelines?`,
          badge: contractorStats.activeJobs.toString(),
          badgeColor: 'bg-tradecrews-orange text-white'
        });
      }

      // Completed jobs for potential follow-up
      if (contractorStats?.completedJobs && contractorStats.completedJobs > 0) {
        actions.push({
          id: 'leverage-success',
          label: `Leverage ${contractorStats.completedJobs} completed job${contractorStats.completedJobs > 1 ? 's' : ''}`,
          description: 'Use your track record to win more work',
          icon: <CheckCircle className="h-4 w-4" />,
          urgency: 'low',
          action: `I've completed ${contractorStats.completedJobs} job${contractorStats.completedJobs > 1 ? 's' : ''}. Help me leverage this success to win more projects.`,
          badge: contractorStats.completedJobs.toString(),
          badgeColor: 'bg-green-500 text-white'
        });
      }
    }

    // Admin-specific actions
    if (userRole === 'admin') {
      // Platform health indicators would go here
      actions.push({
        id: 'platform-health',
        label: 'Check platform health',
        description: 'Review key metrics and alerts',
        icon: <CheckCircle className="h-4 w-4" />,
        urgency: 'medium',
        action: 'Show me the current platform health metrics and any alerts that need attention.',
        badge: null,
        badgeColor: ''
      });
    }

    // If no smart actions, show context-aware defaults
    if (actions.length === 0) {
      return getContextualFallbackActions(pathname, userRole);
    }

    return actions;
  };

  const getContextualFallbackActions = (pathname: string, userRole: string) => {
    if (pathname.includes('/projects/')) {
      return userRole === 'homeowner' 
        ? [
            {
              id: 'manage-project',
              label: 'Manage this project',
              description: 'Get help with project management',
              icon: <Target className="h-4 w-4" />,
              urgency: 'low',
              action: 'Help me manage this project effectively',
              badge: null,
              badgeColor: ''
            }
          ]
        : [
            {
              id: 'bid-project',
              label: 'Analyze for bidding',
              description: 'Get bidding insights for this project',
              icon: <TrendingUp className="h-4 w-4" />,
              urgency: 'low',
              action: 'Help me analyze this project for a competitive bid',
              badge: null,
              badgeColor: ''
            }
          ];
    }

    return [
      {
        id: 'get-started',
        label: 'Get started',
        description: 'Learn how I can help you',
        icon: <ArrowRight className="h-4 w-4" />,
        urgency: 'low',
        action: 'Show me how you can help me get the most out of TradeCrews',
        badge: null,
        badgeColor: ''
      }
    ];
  };

  const isLoading = accountStatsLoading || contractorStatsLoading || pendingBidsLoading;
  const smartActions = getSmartActions();

  if (isLoading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-4 w-32" />
        <div className="space-y-2">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-12 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <h4 className="font-semibold text-sm text-foreground flex items-center gap-2">
        <Zap className="h-4 w-4 text-tradecrews-orange" />
        Smart suggestions for you:
      </h4>
      
      <div className="space-y-2">
        {smartActions.slice(0, 3).map((action) => (
          <Button
            key={action.id}
            variant="outline"
            size="sm"
            onClick={() => onQuickAction(action.action)}
            className={`justify-start text-left h-auto p-3 border-tradecrews-blue/20 hover:bg-tradecrews-blue-50 hover:border-tradecrews-blue dark:border-tradecrews-blue/30 dark:hover:bg-tradecrews-blue-950/50 ${
              action.urgency === 'high' ? 'border-red-200 hover:border-red-400 dark:border-red-800' : ''
            }`}
          >
            <div className="flex items-start gap-3 w-full">
              <div className={`rounded-full p-1.5 ${
                action.urgency === 'high' 
                  ? 'bg-red-100 text-red-600 dark:bg-red-900/50 dark:text-red-400' 
                  : action.urgency === 'medium'
                  ? 'bg-tradecrews-orange-100 text-tradecrews-orange-600 dark:bg-tradecrews-orange-900/50 dark:text-tradecrews-orange-400'
                  : 'bg-tradecrews-blue-100 text-tradecrews-blue-600 dark:bg-tradecrews-blue-900/50 dark:text-tradecrews-blue-400'
              }`}>
                {action.icon}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium text-sm">{action.label}</span>
                  {action.badge && (
                    <Badge className={`text-xs ${action.badgeColor}`}>
                      {action.badge}
                    </Badge>
                  )}
                  {action.urgency === 'high' && (
                    <AlertCircle className="h-3 w-3 text-red-500" />
                  )}
                </div>
                <p className="text-muted-foreground text-xs">{action.description}</p>
              </div>
            </div>
          </Button>
        ))}
      </div>

      {smartActions.length > 3 && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onQuickAction("Show me all my personalized suggestions and insights")}
          className="w-full text-tradecrews-blue hover:text-tradecrews-blue-600"
        >
          <ArrowRight className="mr-2 h-4 w-4" />
          Show {smartActions.length - 3} more suggestions
        </Button>
      )}
    </div>
  );
}