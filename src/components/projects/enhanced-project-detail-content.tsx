"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  AlertCircleIcon,
  BarChart3Icon,
  BuildingIcon,
  CalendarIcon,
  CheckCircleIcon,
  ClockIcon,
  DollarSignIcon,
  FileTextIcon,
  ImageIcon,
  MapPinIcon,
  MessageSquareIcon,
  TrendingUpIcon,
  UsersIcon,
} from "lucide-react";
import Image from "next/image";
import { BidCard } from "@/components/bid/bid-card";
import { PusherChat } from "@/components/chat";
import { CrewList } from "@/components/contractor/crew-list";
import { ImageViewerDialog } from "@/components/job/image-viewer-dialog";
import { HomeownerBidFlowTour } from "@/components/tours/homeowner-bid-flow-tour";
import { useTRPC } from "@/components/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import type { Bid, Job } from "@/db/schema";
import { getStatusVariant, JOB_STATUS_VARIANTS } from "@/lib/utils";
import { EnhancedProjectActions } from "./enhanced-project-actions";

interface EnhancedProjectDetailContentProps {
  jobId: string;
  userId: string;
  userRole: string;
}

export function EnhancedProjectDetailContent({
  jobId,
  userId,
  userRole,
}: EnhancedProjectDetailContentProps) {
  const trpc = useTRPC();

  const { data: job } = useQuery(
    trpc.projects.getById.queryOptions({
      id: jobId,
    }),
  );

  if (!job) {
    return <div>Loading...</div>;
  }

  // Type assertion with proper Job type
  const typedJob = job as unknown as Job;

  const isHomeowner = userRole === "homeowner";
  const isQuickHire = typedJob.jobType === "QUICK_HIRE";
  const acceptedBid = typedJob.bids?.find(
    (bid: Bid) => bid.status === "ACCEPTED",
  );

  // Calculate project progress based on status
  const getProjectProgress = () => {
    switch (typedJob.status) {
      case "DRAFT":
        return 10;
      case "PUBLISHED":
        return 25;
      case "AWARDED":
        return 60;
      case "COMPLETED":
        return 100;
      case "CANCELED":
        return 0;
      case "CLOSED":
        return 90;
      default:
        return 0;
    }
  };

  const getStatusColor = () => {
    switch (typedJob.status) {
      case "COMPLETED":
        return "text-green-600";
      case "AWARDED":
        return "text-tradecrews-blue";
      case "PUBLISHED":
        return "text-tradecrews-orange";
      case "CANCELED":
        return "text-red-600";
      case "CLOSED":
        return "text-gray-600";
      default:
        return "text-gray-600";
    }
  };

  const getStatusIcon = () => {
    switch (typedJob.status) {
      case "COMPLETED":
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case "AWARDED":
        return <TrendingUpIcon className="h-5 w-5 text-tradecrews-blue" />;
      case "PUBLISHED":
        return <AlertCircleIcon className="h-5 w-5 text-tradecrews-orange" />;
      case "CLOSED":
        return <CheckCircleIcon className="h-5 w-5 text-gray-600" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {isHomeowner &&
        typedJob.status === "PUBLISHED" &&
        typedJob.bids &&
        typedJob.bids.length > 0 && <HomeownerBidFlowTour />}

      {/* Hero Section with Project Overview */}
      <div className="relative overflow-hidden rounded-xl bg-linear-to-br from-tradecrews-blue-600 via-white to-tradecrews-orange-500 p-6 sm:p-8">
        <div className="relative z-10">
          <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center gap-3">
              {getStatusIcon()}
              <div>
                <h1 className="font-bold text-2xl sm:text-3xl">
                  {typedJob.name}
                </h1>
                <p className="text-muted-foreground">
                  {typedJob.property?.name} •{" "}
                  {typedJob.jobType.replace("_", " ")}
                </p>
              </div>
            </div>
            <Badge
              variant={getStatusVariant(typedJob.status, JOB_STATUS_VARIANTS)}
              className="w-fit text-sm"
            >
              {typedJob.status.replace("_", " ")}
            </Badge>
          </div>

          {/* Project Progress */}
          <div className="mb-6">
            <div className="mb-2 flex items-center justify-between">
              <span className="font-medium text-sm">Project Progress</span>
              <span className={`font-semibold text-sm ${getStatusColor()}`}>
                {getProjectProgress()}%
              </span>
            </div>
            <Progress value={getProjectProgress()} className="h-2" />
          </div>

          {/* Quick Stats Grid */}
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
            <div className="rounded-lg border border-tradecrews-orange/20 bg-white p-4 shadow-sm">
              <div className="flex items-center gap-2">
                <div className="rounded-full bg-tradecrews-orange-100 p-2">
                  <DollarSignIcon className="h-4 w-4 text-tradecrews-orange-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-600 text-xs">Budget</p>
                  <p className="font-bold text-gray-900">
                    ${typedJob.budget?.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            <div className="rounded-lg border border-tradecrews-blue/20 bg-white p-4 shadow-sm">
              <div className="flex items-center gap-2">
                <div className="rounded-full bg-tradecrews-blue-100 p-2">
                  <CalendarIcon className="h-4 w-4 text-tradecrews-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-600 text-xs">
                    Start Date
                  </p>
                  <p className="font-bold text-gray-900 text-sm">
                    {format(typedJob.startsAt, "MMM d")}
                  </p>
                </div>
              </div>
            </div>

            <div className="rounded-lg border border-tradecrews-orange/20 bg-white p-4 shadow-sm">
              <div className="flex items-center gap-2">
                <div className="rounded-full bg-tradecrews-orange-100 p-2">
                  <ClockIcon className="h-4 w-4 text-tradecrews-orange-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-600 text-xs">Deadline</p>
                  <p className="font-bold text-gray-900 text-sm">
                    {format(typedJob.deadline, "MMM d")}
                  </p>
                </div>
              </div>
            </div>

            <div className="rounded-lg border border-tradecrews-blue/20 bg-white p-4 shadow-sm">
              <div className="flex items-center gap-2">
                <div className="rounded-full bg-tradecrews-blue-100 p-2">
                  <UsersIcon className="h-4 w-4 text-tradecrews-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-600 text-xs">Bids</p>
                  <p className="font-bold text-gray-900">
                    {typedJob.bids?.length || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute top-0 right-0 h-32 w-32 rounded-full bg-tradecrews-orange-200/30 blur-3xl" />
        <div className="absolute bottom-0 left-0 h-24 w-24 rounded-full bg-tradecrews-blue-200/30 blur-2xl" />
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Left Column - Main Content */}
        <div className="space-y-6 lg:col-span-2">
          {/* Property Information */}
          <Card className="border-l-4 border-l-tradecrews-orange">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BuildingIcon className="h-5 w-5 text-tradecrews-orange" />
                Property Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-lg">
                    {typedJob.property?.name}
                  </h3>
                  {typedJob.property?.address && (
                    <div className="mt-2 flex items-start gap-2">
                      <MapPinIcon className="mt-0.5 h-4 w-4 flex-shrink-0 text-tradecrews-orange" />
                      <div>
                        <p className="text-muted-foreground">
                          {typedJob.property.address.street}
                        </p>
                        <p className="text-muted-foreground">
                          {typedJob.property.address.city},{" "}
                          {typedJob.property.address.state}{" "}
                          {typedJob.property.address.zip}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {typedJob.property?.description && (
                  <div>
                    <h4 className="mb-2 font-medium">Property Description</h4>
                    <p className="text-muted-foreground text-sm">
                      {typedJob.property.description}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Project Details Tabs */}
          <Card>
            <CardContent className="p-0">
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger
                    value="overview"
                    className="flex items-center gap-2"
                  >
                    <FileTextIcon className="h-4 w-4" />
                    Overview
                  </TabsTrigger>
                  <TabsTrigger
                    value="images"
                    className="flex items-center gap-2"
                  >
                    <ImageIcon className="h-4 w-4" />
                    Images ({typedJob.images?.length || 0})
                  </TabsTrigger>
                  <TabsTrigger
                    value="tasks"
                    className="flex items-center gap-2"
                  >
                    <BarChart3Icon className="h-4 w-4" />
                    Tasks ({typedJob.tasks?.length || 0})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="p-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="mb-2 font-semibold">
                        Project Information
                      </h3>
                      <p className="text-muted-foreground leading-relaxed">
                        This project is scheduled to begin on{" "}
                        {format(typedJob.startsAt, "MMMM d, yyyy")} with a
                        deadline of {format(typedJob.deadline, "MMMM d, yyyy")}.
                      </p>
                    </div>

                    <Separator />

                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <h4 className="mb-2 font-medium">Timeline</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              Created:
                            </span>
                            <span>{format(typedJob.createdAt, "PPP")}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              Start Date:
                            </span>
                            <span>{format(typedJob.startsAt, "PPP")}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              Deadline:
                            </span>
                            <span>{format(typedJob.deadline, "PPP")}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="mb-2 font-medium">Project Details</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Type:</span>
                            <span>{typedJob.jobType.replace("_", " ")}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              Budget:
                            </span>
                            <span className="font-medium text-tradecrews-orange">
                              ${typedJob.budget?.toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              Status:
                            </span>
                            <Badge
                              variant={getStatusVariant(
                                typedJob.status,
                                JOB_STATUS_VARIANTS,
                              )}
                            >
                              {typedJob.status.replace("_", " ")}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="images" className="p-6">
                  {typedJob.images && typedJob.images.length > 0 ? (
                    <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
                      {typedJob.images.map((image, index: number) => (
                        <div
                          key={image.id}
                          className="group relative overflow-hidden rounded-lg border transition-all hover:shadow-lg"
                        >
                          <ImageViewerDialog
                            images={
                              typedJob.images?.map((img) => ({
                                url: img.url,
                                description: img.description,
                              })) || []
                            }
                            initialIndex={index}
                            title={`${typedJob.name} - Project Images`}
                          >
                            <div className="relative aspect-square cursor-pointer">
                              <Image
                                src={image.url}
                                alt={
                                  image.description ||
                                  `Project image ${index + 1}`
                                }
                                fill
                                className="object-cover transition-transform group-hover:scale-105"
                              />
                              <div className="absolute inset-0 bg-black/0 transition-colors group-hover:bg-black/10" />
                            </div>
                          </ImageViewerDialog>
                          {/* Image description would go here if available */}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-12 text-center">
                      <ImageIcon className="mx-auto mb-4 h-12 w-12 text-muted-foreground/50" />
                      <p className="text-muted-foreground">
                        No images uploaded yet.
                      </p>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="tasks" className="p-6">
                  {typedJob.tasks && typedJob.tasks.length > 0 ? (
                    <div className="space-y-3">
                      {typedJob.tasks.map((task, index: number) => (
                        <div
                          key={task.id}
                          className="flex items-start gap-3 rounded-lg border p-4 transition-colors hover:bg-muted/50"
                        >
                          <div className="mt-1 flex h-6 w-6 items-center justify-center rounded-full bg-tradecrews-orange/10 font-medium text-tradecrews-orange text-xs">
                            {index + 1}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium">{task.name}</h4>
                            {/* Task description would go here if available */}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-12 text-center">
                      <BarChart3Icon className="mx-auto mb-4 h-12 w-12 text-muted-foreground/50" />
                      <p className="text-muted-foreground">
                        No tasks defined yet.
                      </p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Project Actions */}
          <EnhancedProjectActions
            job={typedJob}
            userRole={userRole}
            layout="card"
          />

          {/* Bids/Messages Section */}
          <Card className="border-l-4 border-l-tradecrews-blue">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {isQuickHire ? (
                  <>
                    <MessageSquareIcon className="h-5 w-5 text-tradecrews-blue" />
                    Messages
                  </>
                ) : (
                  <>
                    <UsersIcon className="h-5 w-5 text-tradecrews-blue" />
                    Bids{" "}
                    {typedJob.bids?.length ? `(${typedJob.bids.length})` : ""}
                  </>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isQuickHire ? (
                <PusherChat jobId={typedJob.id} userId={userId} />
              ) : typedJob.bids && typedJob.bids.length > 0 ? (
                <div className="space-y-4">
                  {typedJob.bids.map((bid) => (
                    <BidCard
                      key={bid.id}
                      bidId={bid.id}
                      showAcceptButton={
                        isHomeowner && typedJob.status === "PUBLISHED"
                      }
                    />
                  ))}
                </div>
              ) : (
                <div className="py-8 text-center">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-tradecrews-blue/10">
                    <UsersIcon className="h-8 w-8 text-tradecrews-blue" />
                  </div>
                  <p className="text-muted-foreground">
                    No bids have been submitted for this job yet.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Accepted Contractor Info */}
          {acceptedBid && (
            <Card className="border-l-4 border-l-green-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircleIcon className="h-5 w-5 text-green-600" />
                  Selected Contractor
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold">Selected Contractor</h3>
                  <p className="text-muted-foreground text-sm">
                    Bid Amount: ${acceptedBid.amount?.toLocaleString()}
                  </p>
                  <p className="text-muted-foreground text-sm">
                    Duration: {acceptedBid.estimatedDuration} days
                  </p>
                </div>

                <Separator />

                <div>
                  <h4 className="mb-2 font-medium">Crew Members</h4>
                  <CrewList organizationId={acceptedBid.organizationId} />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Project Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3Icon className="h-5 w-5 text-tradecrews-orange" />
                Project Stats
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground text-sm">
                    Total Bids
                  </span>
                  <span className="font-medium">
                    {typedJob.bids?.length || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground text-sm">Images</span>
                  <span className="font-medium">
                    {typedJob.images?.length || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground text-sm">Tasks</span>
                  <span className="font-medium">
                    {typedJob.tasks?.length || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground text-sm">
                    Days Until Deadline
                  </span>
                  <span className="font-medium">
                    {Math.ceil(
                      (new Date(typedJob.deadline).getTime() - Date.now()) /
                        (1000 * 60 * 60 * 24),
                    )}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Bottom spacing for mobile actionbar */}
      <div className="h-20 md:h-0" />
    </div>
  );
}
