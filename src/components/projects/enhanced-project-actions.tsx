"use client";

import React from "react";
import { useMutation } from "@tanstack/react-query";
import { 
  Pencil, 
  Send, 
  Calendar,
  CheckCircle,
  XCircle,
  MessageSquare,
  Eye,
  Clock,
  Star,
  Download,
  Share2,
  MoreHorizontal
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { useTRPC } from "@/components/trpc/client";
import type { Job } from "@/db/schema";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import { Card, CardContent } from "../ui/card";
import { Separator } from "../ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";

interface EnhancedProjectActionsProps {
  job: Job;
  userRole: string;
  layout?: "horizontal" | "vertical" | "mobile" | "actionbar" | "card";
}

export function EnhancedProjectActions({
  job,
  userRole,
  layout = "card",
}: EnhancedProjectActionsProps) {
  const router = useRouter();
  const trpc = useTRPC();
  const [isPublishing, setIsPublishing] = useState(false);

  const publishJob = useMutation(
    trpc.jobs.publish.mutationOptions({
      onSuccess: () => {
        toast.success("Project published successfully");
        router.refresh();
      },
      onError: (error) => {
        toast.error(`Error publishing project: ${error.message}`);
      },
      onSettled: () => {
        setIsPublishing(false);
      },
    }),
  );

  const handlePublish = () => {
    setIsPublishing(true);
    publishJob.mutate({ id: job.id });
  };

  // Define action buttons based on user role and job status
  const isHomeowner = userRole === "homeowner";
  const isProfessional = userRole === "contractor";
  const isDraft = job.status === "DRAFT";
  const isPublished = job.status === "PUBLISHED";
  const isAwarded = job.status === "AWARDED";
  const isCompleted = job.status === "COMPLETED";
  const isCanceled = job.status === "CANCELED";

  // Get status info for display
  const getStatusInfo = () => {
    switch (job.status) {
      case "DRAFT":
        return {
          color: "text-yellow-600",
          bg: "bg-yellow-50",
          border: "border-yellow-200",
          icon: <Clock className="h-4 w-4" />,
          label: "Draft"
        };
      case "PUBLISHED":
        return {
          color: "text-tradecrews-orange",
          bg: "bg-tradecrews-orange-50",
          border: "border-tradecrews-orange-200",
          icon: <Eye className="h-4 w-4" />,
          label: "Published"
        };
      case "AWARDED":
        return {
          color: "text-tradecrews-blue",
          bg: "bg-tradecrews-blue-50",
          border: "border-tradecrews-blue-200",
          icon: <Star className="h-4 w-4" />,
          label: "Awarded"
        };
      case "COMPLETED":
        return {
          color: "text-green-600",
          bg: "bg-green-50",
          border: "border-green-200",
          icon: <CheckCircle className="h-4 w-4" />,
          label: "Completed"
        };
      case "CANCELED":
        return {
          color: "text-red-600",
          bg: "bg-red-50",
          border: "border-red-200",
          icon: <XCircle className="h-4 w-4" />,
          label: "Canceled"
        };
      default:
        return {
          color: "text-gray-600",
          bg: "bg-gray-50",
          border: "border-gray-200",
          icon: <Clock className="h-4 w-4" />,
          label: "Unknown"
        };
    }
  };

  const statusInfo = getStatusInfo();

  // Primary actions based on role and status
  const getPrimaryActions = () => {
    const actions = [];

    if (isHomeowner) {
      if (isDraft) {
        actions.push(
          <Button
            key="edit"
            asChild
            variant="outline"
            size="sm"
            className="border-tradecrews-orange text-tradecrews-orange hover:bg-tradecrews-orange hover:text-white"
          >
            <Link href={`/projects/${job.id}/edit`}>
              <Pencil className="mr-2 h-4 w-4" />
              Edit Project
            </Link>
          </Button>
        );
        actions.push(
          <Button
            key="publish"
            onClick={handlePublish}
            disabled={isPublishing}
            size="sm"
            className="bg-tradecrews-orange hover:bg-tradecrews-orange-600"
          >
            <Send className="mr-2 h-4 w-4" />
            {isPublishing ? "Publishing..." : "Publish"}
          </Button>
        );
      } else if (isPublished) {
        actions.push(
          <Button
            key="edit"
            asChild
            variant="outline"
            size="sm"
            className="border-tradecrews-orange text-tradecrews-orange hover:bg-tradecrews-orange hover:text-white"
          >
            <Link href={`/projects/${job.id}/edit`}>
              <Pencil className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>
        );
      } else if (isAwarded) {
        actions.push(
          <Button
            key="schedule"
            asChild
            size="sm"
            className="bg-tradecrews-blue hover:bg-tradecrews-blue-600"
          >
            <Link href={`/projects/${job.id}/schedule`}>
              <Calendar className="mr-2 h-4 w-4" />
              Schedule
            </Link>
          </Button>
        );
        actions.push(
          <Button
            key="complete"
            asChild
            variant="outline"
            size="sm"
            className="border-green-600 text-green-600 hover:bg-green-600 hover:text-white"
          >
            <Link href={`/projects/${job.id}/complete`}>
              <CheckCircle className="mr-2 h-4 w-4" />
              Mark Complete
            </Link>
          </Button>
        );
      } else if (isCompleted) {
        actions.push(
          <Button
            key="review"
            asChild
            size="sm"
            className="bg-tradecrews-orange hover:bg-tradecrews-orange-600"
          >
            <Link href={`/projects/${job.id}/review`}>
              <Star className="mr-2 h-4 w-4" />
              Leave Review
            </Link>
          </Button>
        );
      }
    }

    if (isProfessional && isPublished) {
      actions.push(
        <Button
          key="bid"
          asChild
          size="sm"
          className="bg-tradecrews-blue hover:bg-tradecrews-blue-600"
        >
          <Link href={`/projects/${job.id}/bid`}>
            <Send className="mr-2 h-4 w-4" />
            Submit Bid
          </Link>
        </Button>
      );
    }

    return actions;
  };

  // Secondary actions for dropdown
  const getSecondaryActions = () => {
    const actions = [];

    if (isHomeowner && !isCompleted && !isCanceled) {
      actions.push(
        <DropdownMenuItem key="cancel" asChild>
          <Link href={`/projects/${job.id}/cancel`} className="text-red-600">
            <XCircle className="mr-2 h-4 w-4" />
            Cancel Project
          </Link>
        </DropdownMenuItem>
      );
    }

    actions.push(
      <DropdownMenuItem key="share">
        <Share2 className="mr-2 h-4 w-4" />
        Share Project
      </DropdownMenuItem>
    );

    if (isCompleted) {
      actions.push(
        <DropdownMenuItem key="download">
          <Download className="mr-2 h-4 w-4" />
          Download Report
        </DropdownMenuItem>
      );
    }

    return actions;
  };

  const primaryActions = getPrimaryActions();
  const secondaryActions = getSecondaryActions();

  // Card layout for sidebar
  if (layout === "card") {
    return (
      <Card className="border-l-4 border-l-tradecrews-orange">
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Status Display */}
            <div className="flex items-center gap-3">
              <div className={`rounded-full p-2 ${statusInfo.bg} ${statusInfo.border} border`}>
                <div className={statusInfo.color}>
                  {statusInfo.icon}
                </div>
              </div>
              <div>
                <h3 className="font-semibold">Project Status</h3>
                <p className={`text-sm ${statusInfo.color}`}>{statusInfo.label}</p>
              </div>
            </div>

            <Separator />

            {/* Primary Actions */}
            {primaryActions.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium text-sm text-gray-700">Quick Actions</h4>
                <div className="flex flex-col gap-2">
                  {primaryActions}
                </div>
              </div>
            )}

            {/* Secondary Actions */}
            {secondaryActions.length > 0 && (
              <>
                <Separator />
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="w-full">
                      <MoreHorizontal className="mr-2 h-4 w-4" />
                      More Actions
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    {secondaryActions.map((action, index) => (
                      <React.Fragment key={index}>
                        {action}
                        {index < secondaryActions.length - 1 && <DropdownMenuSeparator />}
                      </React.Fragment>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Horizontal layout for desktop header
  if (layout === "horizontal") {
    return (
      <div className="flex items-center gap-2">
        {primaryActions.slice(0, 2)}
        {secondaryActions.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {secondaryActions}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    );
  }

  // Action bar layout for mobile
  if (layout === "actionbar") {
    return (
      <div className="flex w-full gap-2">
        {primaryActions.length > 0 ? (
          <>
            {primaryActions[0]}
            {primaryActions.length > 1 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {primaryActions.slice(1).map((action, index) => (
                    <DropdownMenuItem key={index} asChild>
                      {action}
                    </DropdownMenuItem>
                  ))}
                  {secondaryActions.length > 0 && <DropdownMenuSeparator />}
                  {secondaryActions}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </>
        ) : (
          <Button variant="outline" size="sm" className="w-full">
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </Button>
        )}
      </div>
    );
  }

  // Default vertical layout
  return (
    <div className="flex flex-col gap-2">
      {primaryActions}
      {secondaryActions.length > 0 && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <MoreHorizontal className="mr-2 h-4 w-4" />
              More Actions
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {secondaryActions}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
}