"use client";

import { useQuery } from "@tanstack/react-query";
import {
  AlertCircle,
  BarChart3,
  CheckCircle,
  Clock,
  DollarSign,
  MapPin,
  TrendingUp,
} from "lucide-react";
import { useTRPC } from "@/components/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export function PropertyAnalytics() {
  const trpc = useTRPC();

  const { data: properties, isLoading: isLoadingProperties } = useQuery(
    trpc.properties.list.queryOptions(),
  );

  // This would be a new tRPC procedure to get property analytics
  // const { data: analytics, isLoading: isLoadingAnalytics } = useQuery(
  //   trpc.properties.getAnalytics.queryOptions()
  // );

  // Mock analytics data for demonstration
  const analytics = {
    totalProperties: properties?.length || 0,
    totalValue: 0, // Would come from property values/project costs
    averageProjectsPerProperty: 0,
    topPerformingProperty: null,
    recentActivity: [],
    locationDistribution: {},
    projectTimeline: [],
  };

  if (isLoadingProperties) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            // biome-ignore lint/suspicious/noArrayIndexKey: Static skeleton items
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="mb-2 h-8 w-16" />
                <Skeleton className="h-3 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!properties || properties.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <BarChart3 className="mb-4 h-12 w-12 text-muted-foreground" />
          <h3 className="mb-2 font-semibold text-lg">No Analytics Available</h3>
          <p className="text-center text-muted-foreground">
            Add properties and complete projects to see analytics and insights.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Calculate basic analytics from available data
  const locationStats = properties.reduce(
    (acc, property) => {
      if (property.addressCity) {
        const city = property.addressCity;
        acc[city] = (acc[city] || 0) + 1;
      }
      return acc;
    },
    {} as Record<string, number>,
  );

  const topCities = Object.entries(locationStats)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5);

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="font-medium text-sm">
              Total Properties
            </CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="font-bold text-2xl">{properties.length}</div>
            <p className="text-muted-foreground text-xs">
              Across {Object.keys(locationStats).length} cities
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="font-medium text-sm">
              Active Projects
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="font-bold text-2xl">0</div>
            <p className="text-muted-foreground text-xs">
              Currently in progress
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="font-medium text-sm">
              Completed Projects
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="font-bold text-2xl">0</div>
            <p className="text-muted-foreground text-xs">All time total</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="font-medium text-sm">
              Portfolio Value
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="font-bold text-2xl">$0</div>
            <p className="text-muted-foreground text-xs">
              Total project investment
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="locations">Locations</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {properties.slice(0, 3).map((property) => (
                    <div key={property.id} className="flex items-center gap-3">
                      <div className="h-2 w-2 rounded-full bg-tradecrews-orange-500" />
                      <div className="min-w-0 flex-1">
                        <p className="truncate font-medium text-sm">
                          {property.name}
                        </p>
                        <p className="text-muted-foreground text-xs">
                          Added{" "}
                          {new Date(property.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        New
                      </Badge>
                    </div>
                  ))}
                  {properties.length === 0 && (
                    <p className="text-muted-foreground text-sm">
                      No recent activity
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Property Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Active Properties</span>
                    <Badge variant="default">{properties.length}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">With Projects</span>
                    <Badge variant="secondary">0</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Needs Attention</span>
                    <Badge variant="outline">0</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="locations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">
                Properties by Location
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {topCities.length > 0 ? (
                  topCities.map(([city, count]) => (
                    <div
                      key={city}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium text-sm">{city}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-20 rounded-full bg-muted">
                          <div
                            className="h-2 rounded-full bg-tradecrews-orange-500"
                            style={{
                              width: `${(count / properties.length) * 100}%`,
                            }}
                          />
                        </div>
                        <span className="w-8 text-right text-muted-foreground text-sm">
                          {count}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-muted-foreground text-sm">
                    No location data available
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Performance Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3 rounded-lg bg-muted/50 p-3">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium text-sm">Portfolio Growth</p>
                    <p className="text-muted-foreground text-xs">
                      You've added {properties.length} properties to your
                      portfolio
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 rounded-lg bg-muted/50 p-3">
                  <AlertCircle className="h-5 w-5 text-orange-600" />
                  <div>
                    <p className="font-medium text-sm">Opportunity</p>
                    <p className="text-muted-foreground text-xs">
                      Start your first project to unlock detailed analytics
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
