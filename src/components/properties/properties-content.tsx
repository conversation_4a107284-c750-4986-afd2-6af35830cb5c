"use client";

import { useQuery } from "@tanstack/react-query";
import { NewProperty } from "@/components/properties/new-property";
import { PropertyCard } from "@/components/properties/property-card";
import { useTRPC } from "@/components/trpc/client";
import { CardGrid } from "@/components/ui/responsive-grid";

export function PropertiesContent() {
  const trpc = useTRPC();
  const { data: properties } = useQuery(trpc.properties.list.queryOptions());

  if (!properties || properties.length === 0) {
    return (
      <CardGrid>
        <NewProperty />
      </CardGrid>
    );
  }

  return (
    <CardGrid>
      {properties.map((property) => (
        <PropertyCard key={property.id} property={property as any} />
      ))}
      <NewProperty />
    </CardGrid>
  );
}
