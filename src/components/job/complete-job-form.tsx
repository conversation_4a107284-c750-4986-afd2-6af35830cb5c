"use client";

import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { useTRPC } from "@/components/trpc/client";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useSession } from "@/lib/auth-client";

interface CompleteJobFormProps {
  jobId: string;
  homeownerCompleted?: boolean;
  contractorCompleted?: boolean;
}

export function CompleteJobForm({
  jobId,
  homeownerCompleted = false,
  contractorCompleted = false,
}: CompleteJobFormProps) {
  const router = useRouter();
  const trpc = useTRPC();
  const [confirmed, setConfirmed] = useState(false);
  const { data: session } = useSession();
  const user = session?.user;

  const role = user?.role || "HOMEOWNER";
  const alreadyCompleted =
    role === "HOMEOWNER" ? homeownerCompleted : contractorCompleted;
  const otherPartyCompleted =
    role === "HOMEOWNER" ? contractorCompleted : homeownerCompleted;

  const markComplete = useMutation(
    trpc.projects.markComplete.mutationOptions({
      onSuccess: () => {
        toast.success(
          otherPartyCompleted
            ? "Project marked as complete!"
            : "Project marked as complete on your end. Waiting for the other party to confirm.",
        );
        router.refresh();
      },
      onError: (error) => {
        toast.error(`Error marking project as complete: ${error.message}`);
      },
    }),
  );

  const handleComplete = () => {
    if (!confirmed) {
      toast.error(
        "Please confirm that you want to mark this project as complete",
      );
      return;
    }

    markComplete.mutate({
      jobId,
      role: role === "admin" ? "homeowner" : role,
    });
  };

  if (alreadyCompleted) {
    return (
      <div className="rounded-md bg-muted p-4">
        <p className="text-muted-foreground text-sm">
          You have already marked this project as complete.
          {!otherPartyCompleted && " Waiting for the other party to confirm."}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-start space-x-2">
        <Checkbox
          id="confirm"
          checked={confirmed}
          onCheckedChange={(checked) => setConfirmed(checked as boolean)}
        />
        <div className="grid gap-1.5 leading-none">
          <Label
            htmlFor="confirm"
            className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            I confirm that:
          </Label>
          <ul className="text-muted-foreground text-sm">
            <li>• All work has been completed to my satisfaction</li>
            <li>• All agreed-upon deliverables have been provided</li>
            <li>• I am ready to mark this project as complete</li>
          </ul>
        </div>
      </div>

      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={() => router.back()}>
          Cancel
        </Button>
        <Button
          type="button"
          className="bg-green-600 hover:bg-green-700"
          onClick={handleComplete}
          disabled={markComplete.isPending}
        >
          {markComplete.isPending ? "Processing..." : "Mark as Complete"}
        </Button>
      </div>
    </div>
  );
}
