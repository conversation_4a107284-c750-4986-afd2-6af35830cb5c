import {
  Body,
  Button,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

interface TradeCrewsLaunchEmailProps {
  userId?: string;
  recipientEmail?: string;
}

const TradeCrewsLaunchEmail = ({
  userId,
  recipientEmail,
}: TradeCrewsLaunchEmailProps = {}) => {
  return (
    <Html>
      <Head />
      <Preview>
        🎉 Welcome to TradeCrews! Your home improvement journey starts here
      </Preview>
      <Tailwind>
        <Body className="bg-slate-50 py-10 font-sans">
          <Container className="mx-auto max-w-2xl bg-white rounded-lg overflow-hidden">
            {/* Hero Header */}
            <Section className="bg-orange-500 px-8 py-8 text-center">
              <Img
                src="https://beta.tradecrews.com/images/tc-logo.webp"
                width="120"
                height="40"
                alt="TradeCrews"
                className="mx-auto mb-[20px]"
              />
              <Heading className="m-0 font-bold text-[28px] text-white">
                Welcome to TradeCrews! 🎉
              </Heading>
              <Text className="m-0 mt-[12px] text-[18px] text-orange-100">
                Your home improvement journey starts here
              </Text>
            </Section>

            {/* Jack Introduction */}
            <Section className="px-[32px] py-[32px]">
              <div className="mb-[24px] flex items-center justify-center">
                <div className="rounded-full bg-gradient-to-r from-blue-500 to-blue-600 p-[3px]">
                  <div className="rounded-full bg-white p-[12px]">
                    <Text className="m-0 text-[24px]">🤖</Text>
                  </div>
                </div>
              </div>

              <Text className="mb-[24px] text-center font-medium text-[20px] text-gray-800">
                Hey there! 👋 I'm{" "}
                <span className="font-bold text-blue-600">Jack</span>, your AI
                assistant
              </Text>

              <Text className="mb-[32px] text-center text-[16px] text-gray-600 leading-relaxed">
                I'm here to make your home improvement projects as smooth and
                successful as possible. Think of me as your personal guide
                through every step of the process!
              </Text>

              {/* Features Grid */}
              <Section className="mb-[32px]">
                <Text className="mb-[20px] text-center font-bold text-[18px] text-gray-800">
                  Here's how I'll help you succeed:
                </Text>

                <div className="grid grid-cols-1 gap-[16px]">
                  <div className="flex items-start rounded-[12px] border border-blue-100 bg-blue-50 p-[16px]">
                    <span className="mr-[12px] text-[20px]">🎯</span>
                    <div>
                      <Text className="m-0 mb-[4px] font-bold text-[16px] text-blue-800">
                        Find Perfect Professionals
                      </Text>
                      <Text className="m-0 text-[14px] text-blue-700">
                        Match with verified experts who have the right skills
                        for your project
                      </Text>
                    </div>
                  </div>

                  <div className="flex items-start rounded-[12px] border border-green-100 bg-green-50 p-[16px]">
                    <span className="mr-[12px] text-[20px]">📊</span>
                    <div>
                      <Text className="m-0 mb-[4px] font-bold text-[16px] text-green-800">
                        Compare Bids Smartly
                      </Text>
                      <Text className="m-0 text-[14px] text-green-700">
                        Get guidance on evaluating quotes and choosing the best
                        option
                      </Text>
                    </div>
                  </div>

                  <div className="flex items-start rounded-[12px] border border-purple-100 bg-purple-50 p-[16px]">
                    <span className="mr-[12px] text-[20px]">💬</span>
                    <div>
                      <Text className="m-0 mb-[4px] font-bold text-[16px] text-purple-800">
                        24/7 Support
                      </Text>
                      <Text className="m-0 text-[14px] text-purple-700">
                        Get answers to your questions anytime, from planning to
                        completion
                      </Text>
                    </div>
                  </div>

                  <div className="flex items-start rounded-[12px] border border-orange-100 bg-orange-50 p-[16px]">
                    <span className="mr-[12px] text-[20px]">📋</span>
                    <div>
                      <Text className="m-0 mb-[4px] font-bold text-[16px] text-orange-800">
                        Stay Organized
                      </Text>
                      <Text className="m-0 text-[14px] text-orange-700">
                        Manage all your projects from start to finish in one
                        place
                      </Text>
                    </div>
                  </div>
                </div>
              </Section>

              {/* Main CTA */}
              <Section className="mb-[32px] text-center">
                <Button
                  className="inline-block rounded-[12px] bg-gradient-to-r from-blue-600 to-blue-700 px-[40px] py-[16px] text-center font-bold text-[18px] text-white no-underline shadow-lg transition-all hover:shadow-xl"
                  href="https://tradecrews.com/dashboard"
                >
                  🚀 Let's Get Started!
                </Button>
                <Text className="mt-[12px] text-[14px] text-gray-500">
                  Start your first project in just a few clicks
                </Text>
              </Section>

              {/* Professional Section */}
              <Section className="mb-[32px] rounded-[12px] border-2 border-blue-100 bg-gradient-to-br from-gray-50 to-blue-50 p-[24px]">
                <Text className="mb-[16px] text-center font-bold text-[18px] text-gray-800">
                  🔧 Are you a skilled tradesperson?
                </Text>
                <Text className="mb-[20px] text-center text-[16px] text-gray-600">
                  Join our community of professionals! I'll help you grow your
                  business by connecting you with quality projects and
                  supporting you every step of the way.
                </Text>

                <div className="text-center">
                  <Button
                    className="inline-block rounded-[8px] border-2 border-orange-500 bg-white px-[32px] py-[12px] text-center font-bold text-[16px] text-orange-600 no-underline transition-all hover:bg-orange-50"
                    href="https://tradecrews.com/sign-up/contractor"
                  >
                    👷‍♂️ Join as a Professional
                  </Button>
                </div>
              </Section>

              {/* Getting Started Tips */}
              <Section className="mb-[32px] rounded-[12px] border border-green-200 bg-green-50 p-[20px]">
                <Text className="mb-[12px] font-bold text-[16px] text-green-800">
                  💡 Quick Start Tips:
                </Text>
                <ul className="m-0 space-y-[6px] pl-[20px] text-[14px] text-green-700">
                  <li>Click the chat button anytime to talk with me</li>
                  <li>
                    Start by posting your first project or browsing
                    professionals
                  </li>
                  <li>Check out our verification process for peace of mind</li>
                  <li>Explore our help center for detailed guides</li>
                </ul>
              </Section>

              {/* Jack's Personal Message */}
              <Section className="text-center">
                <Text className="mb-[16px] text-[16px] text-gray-700 leading-relaxed">
                  Ready to transform your home? I'm here whenever you need me -
                  just look for the chat button and say hello! I'm excited to
                  help make your home improvement dreams a reality. 🏠✨
                </Text>

                <Text className="mb-[20px] font-bold text-[18px] text-gray-800">
                  Jack ⚡<br />
                  <span className="font-normal text-[16px] text-blue-600">
                    Your AI Assistant at TradeCrews
                  </span>
                </Text>
              </Section>
            </Section>

            {/* Footer */}
            <Section className="border-gray-200 border-t bg-gray-50 px-[32px] py-[24px]">
              <Text className="mb-[12px] text-center text-[14px] text-gray-600">
                Questions? Chat with me directly on the platform or email{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="font-medium text-blue-600 no-underline"
                >
                  <EMAIL>
                </a>
              </Text>

              <Text className="mb-[8px] text-center text-[12px] text-gray-600">
                © {new Date().getFullYear()} TradeCrews Inc. All rights
                reserved.
              </Text>
              <Text className="mb-[8px] text-center text-[12px] text-gray-500">
                Connecting homeowners with trusted professionals since 2024
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                <a
                  href={
                    userId && recipientEmail
                      ? `https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`
                      : "https://tradecrews.com/unsubscribe"
                  }
                  className="text-gray-500 underline hover:text-gray-700"
                >
                  Unsubscribe
                </a>
                {" | "}
                <a
                  href="https://tradecrews.com/privacy"
                  className="text-gray-500 underline hover:text-gray-700"
                >
                  Privacy Policy
                </a>
                {" | "}
                <a
                  href="https://tradecrews.com/help"
                  className="text-gray-500 underline hover:text-gray-700"
                >
                  Help Center
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default TradeCrewsLaunchEmail;
