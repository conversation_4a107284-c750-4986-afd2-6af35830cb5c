import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

interface NewBidNotificationProps {
  bidName: string;
  bidId: string;
  jobName: string;
  contractorName: string;
  amount: string;
  estimatedDuration: number;
  userId: string;
  recipientEmail: string;
}

const NewBidNotificationEmail = ({
  bidName,
  bidId,
  jobName,
  contractorName,
  amount,
  estimatedDuration,
  userId,
  recipientEmail,
}: NewBidNotificationProps) => {
  return (
    <Html>
      <Head />
      <Preview>
        🎉 Great news! New bid received - ${amount} from {contractorName}
      </Preview>
      <Tailwind>
        <Body className="bg-slate-50 py-10 font-sans">
          <Container className="mx-auto max-w-2xl overflow-hidden rounded-lg bg-white">
            {/* Celebration Header */}
            <Section className="bg-green-500 px-8 py-6 text-center">
              <div className="mb-4">
                <span className="text-5xl">🎉</span>
              </div>
              <Heading className="m-0 font-bold text-2xl text-white">
                Fantastic News!
              </Heading>
              <Text className="m-0 mt-2 text-green-100">
                You've received a new bid on your project
              </Text>
            </Section>

            {/* Main Content */}
            <Section className="px-8 py-8">
              <Text className="mb-6 text-center font-medium text-gray-700 text-lg">
                Hey there! 👋 Jack here with exciting news - a qualified
                professional has submitted a bid for your project!
              </Text>

              {/* Bid Details Card */}
              <Section className="mb-8 rounded-lg border-2 border-green-200 bg-green-50 p-6">
                <Text className="mb-4 border-green-200 border-b pb-3 font-bold text-gray-800 text-xl">
                  {bidName}
                </Text>

                <table className="w-full">
                  <tr>
                    <td className="pb-3">
                      <Text className="m-0 text-base text-gray-700">
                        🏠 <strong className="text-blue-700">Project:</strong>{" "}
                        {jobName}
                      </Text>
                    </td>
                  </tr>
                  <tr>
                    <td className="pb-3">
                      <Text className="m-0 text-base text-gray-700">
                        👷‍♂️{" "}
                        <strong className="text-purple-700">Contractor:</strong>{" "}
                        {contractorName}
                      </Text>
                    </td>
                  </tr>
                  <tr>
                    <td className="pb-3">
                      <Text className="m-0 text-base text-gray-700">
                        💰{" "}
                        <strong className="text-green-700">Bid Amount:</strong>{" "}
                        <span className="font-bold text-green-800 text-lg">
                          ${amount}
                        </span>
                      </Text>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <Text className="m-0 text-base text-gray-700">
                        ⏱️{" "}
                        <strong className="text-orange-700">Timeline:</strong>{" "}
                        {estimatedDuration} days
                      </Text>
                    </td>
                  </tr>
                </table>
              </Section>

              {/* Progress Message */}
              <Section className="mb-8 rounded-lg border-blue-500 border-l-4 bg-blue-50 p-5">
                <Text className="m-0 text-base text-gray-700 leading-relaxed">
                  <strong className="text-blue-700">
                    This is exciting progress!
                  </strong>
                  <br />A qualified contractor has carefully reviewed your
                  project details and submitted their professional proposal.
                  This shows genuine interest in your project and demonstrates
                  their commitment to quality work.
                </Text>
              </Section>

              {/* Call to Action */}
              <Section className="mb-8 text-center">
                <Button
                  className="rounded-lg bg-blue-600 px-10 py-4 text-center font-bold text-lg text-white no-underline"
                  href={`https://tradecrews.com/bids/${bidId}`}
                >
                  📋 Review This Bid
                </Button>

                <Text className="mt-3 text-gray-500 text-sm">
                  See full details, contractor profile, and proposal
                </Text>
              </Section>

              {/* Evaluation Tips */}
              <Section className="mb-8 rounded-lg border border-amber-200 bg-amber-50 p-5">
                <Text className="mb-3 font-bold text-amber-800 text-base">
                  💡 Smart Bid Evaluation Tips:
                </Text>
                <table className="w-full">
                  <tr>
                    <td className="pb-1 text-amber-700 text-sm">
                      • Review the contractor's profile and past work
                    </td>
                  </tr>
                  <tr>
                    <td className="pb-1 text-amber-700 text-sm">
                      • Check their ratings and customer reviews
                    </td>
                  </tr>
                  <tr>
                    <td className="pb-1 text-amber-700 text-sm">
                      • Compare timeline and pricing with other bids
                    </td>
                  </tr>
                  <tr>
                    <td className="pb-1 text-amber-700 text-sm">
                      • Look for detailed project breakdown and materials
                    </td>
                  </tr>
                  <tr>
                    <td className="text-amber-700 text-sm">
                      • Consider asking follow-up questions
                    </td>
                  </tr>
                </table>
              </Section>

              {/* Next Steps */}
              <Section className="mb-6 rounded-lg border border-purple-200 bg-purple-50 p-5">
                <Text className="mb-3 font-bold text-base text-purple-800">
                  🎯 What happens next?
                </Text>
                <table className="w-full">
                  <tr>
                    <td className="pb-2 text-purple-700 text-sm">
                      1️⃣ Review this bid and any others you receive
                    </td>
                  </tr>
                  <tr>
                    <td className="pb-2 text-purple-700 text-sm">
                      2️⃣ Ask questions or request clarifications
                    </td>
                  </tr>
                  <tr>
                    <td className="pb-2 text-purple-700 text-sm">
                      3️⃣ Accept the bid that feels right for your project
                    </td>
                  </tr>
                  <tr>
                    <td className="text-purple-700 text-sm">
                      4️⃣ Schedule and start your project!
                    </td>
                  </tr>
                </table>
              </Section>

              {/* Jack's Signature */}
              <Section className="text-center">
                <Text className="mb-2 text-base text-gray-700">
                  Need help evaluating this bid or have questions? I'm here to
                  guide you! 🤝
                </Text>
                <Text className="mb-4 font-bold text-base text-gray-800">
                  Jack ⚡<br />
                  <span className="font-normal text-blue-600 text-sm">
                    Your AI Assistant at TradeCrews
                  </span>
                </Text>
              </Section>
            </Section>

            {/* Footer */}
            <Section className="border-gray-200 border-t bg-gray-50 px-8 py-6 text-center">
              <Text className="mb-2 text-gray-600 text-xs">
                © {new Date().getFullYear()} TradeCrews Inc. All rights
                reserved.
              </Text>
              <Text className="mb-2 text-gray-500 text-xs">
                Connecting homeowners with trusted professionals
              </Text>
              <Text className="m-0 text-gray-500 text-xs">
                <a
                  href={`https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`}
                  className="text-gray-500 underline"
                >
                  Unsubscribe
                </a>
                {" | "}
                <a
                  href="https://tradecrews.com/privacy"
                  className="text-gray-500 underline"
                >
                  Privacy Policy
                </a>
                {" | "}
                <a
                  href="https://tradecrews.com/help"
                  className="text-gray-500 underline"
                >
                  Help Center
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

NewBidNotificationEmail.PreviewProps = {
  bidName: "New Bid",
  bidId: "123",
  jobName: "New Job",
  contractorName: "John Doe",
  amount: "1000",
  estimatedDuration: 10,
  userId: "123",
  recipientEmail: "<EMAIL>",
};

export default NewBidNotificationEmail;
