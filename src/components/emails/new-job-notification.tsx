import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

interface NewJobNotificationProps {
  jobName: string;
  jobId: string;
  tradeName: string;
  budget: string;
  location: string;
  deadline: string;
  userId: string;
  recipientEmail: string;
}

const NewJobNotificationEmail = ({
  jobName,
  jobId,
  tradeName,
  budget,
  location,
  deadline,
  userId,
  recipientEmail,
}: NewJobNotificationProps) => {
  return (
    <Html>
      <Head />
      <Preview>
        🔨 New {tradeName} opportunity - ${budget} in {location}
      </Preview>
      <Tailwind>
        <Body className="bg-slate-50 py-10 font-sans">
          <Container className="mx-auto max-w-2xl overflow-hidden rounded-lg bg-white">
            {/* Header with Brand */}
            <Section className="bg-blue-600 px-8 py-6 text-center">
              <Img
                src="https://tradecrews.com/images/tc-logomark.webp"
                width="48"
                height="48"
                alt="TradeCrews"
                className="mx-auto mb-4"
              />
              <Heading className="m-0 font-bold text-2xl text-white">
                🔨 New Job Opportunity!
              </Heading>
              <Text className="m-0 mt-2 text-blue-100">
                Perfect match found for your expertise
              </Text>
            </Section>

            {/* Main Content */}
            <Section className="px-8 py-8">
              <Text className="mb-6 text-center font-medium text-gray-700 text-lg">
                Hey there! 👋 Jack here - I found a{" "}
                <span className="font-bold text-blue-600">{tradeName}</span> job
                that looks perfect for you!
              </Text>

              {/* Job Details Card */}
              <Section className="mb-8 rounded-lg border-2 border-orange-200 bg-orange-50 p-6">
                <Text className="mb-4 border-orange-200 border-b pb-3 font-bold text-gray-800 text-xl">
                  {jobName}
                </Text>

                {/* Using table layout for better email client support */}
                <table className="w-full">
                  <tr>
                    <td className="pb-3">
                      <Text className="m-0 text-base text-gray-700">
                        💰 <strong className="text-green-700">Budget:</strong>{" "}
                        <span className="font-bold text-green-800">
                          ${budget}
                        </span>
                      </Text>
                    </td>
                  </tr>
                  <tr>
                    <td className="pb-3">
                      <Text className="m-0 text-base text-gray-700">
                        📍 <strong className="text-blue-700">Location:</strong>{" "}
                        {location}
                      </Text>
                    </td>
                  </tr>
                  <tr>
                    <td className="pb-3">
                      <Text className="m-0 text-base text-gray-700">
                        ⏰ <strong className="text-red-700">Deadline:</strong>{" "}
                        {deadline}
                      </Text>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <Text className="m-0 text-base text-gray-700">
                        🔧 <strong className="text-purple-700">Trade:</strong>{" "}
                        {tradeName}
                      </Text>
                    </td>
                  </tr>
                </table>
              </Section>

              {/* Personalized Message */}
              <Section className="mb-8 rounded-lg border-blue-500 border-l-4 bg-blue-50 p-5">
                <Text className="m-0 text-base text-gray-700 leading-relaxed">
                  <strong className="text-blue-700">
                    Why this is perfect for you:
                  </strong>
                  <br />
                  Based on your {tradeName} expertise and location, this project
                  aligns perfectly with your skills. The homeowner is actively
                  looking for quality work, and your profile suggests you'd be
                  an excellent fit!
                </Text>
              </Section>

              {/* Call to Action */}
              <Section className="mb-8 text-center">
                <Button
                  className="rounded-lg bg-orange-500 px-10 py-4 text-center font-bold text-lg text-white no-underline"
                  href={`https://tradecrews.com/jobs/${jobId}`}
                >
                  🎯 View Job & Submit Bid
                </Button>

                <Text className="mt-3 text-gray-500 text-sm">
                  Click to see full details and submit your proposal
                </Text>
              </Section>

              {/* Tips Section */}
              <Section className="mb-6 rounded-lg border border-green-200 bg-green-50 p-5">
                <Text className="mb-3 font-bold text-base text-green-800">
                  💡 Pro Tips for Winning This Bid:
                </Text>
                <table className="w-full">
                  <tr>
                    <td className="pb-1 text-green-700 text-sm">
                      • Respond quickly - early bids often get more attention
                    </td>
                  </tr>
                  <tr>
                    <td className="pb-1 text-green-700 text-sm">
                      • Include specific details about your {tradeName}{" "}
                      experience
                    </td>
                  </tr>
                  <tr>
                    <td className="pb-1 text-green-700 text-sm">
                      • Mention your availability and timeline
                    </td>
                  </tr>
                  <tr>
                    <td className="text-green-700 text-sm">
                      • Ask thoughtful questions about the project
                    </td>
                  </tr>
                </table>
              </Section>

              {/* Jack's Signature */}
              <Section className="text-center">
                <Text className="mb-2 text-base text-gray-700">
                  Need help crafting the perfect bid? I'm here to assist! 🤝
                </Text>
                <Text className="mb-4 font-bold text-base text-gray-800">
                  Jack ⚡<br />
                  <span className="font-normal text-blue-600 text-sm">
                    Your AI Assistant at TradeCrews
                  </span>
                </Text>
              </Section>
            </Section>

            {/* Footer */}
            <Section className="border-gray-200 border-t bg-gray-50 px-8 py-6 text-center">
              <Text className="mb-2 text-gray-600 text-xs">
                © {new Date().getFullYear()} TradeCrews Inc. All rights
                reserved.
              </Text>
              <Text className="mb-2 text-gray-500 text-xs">
                Connecting skilled professionals with quality projects
              </Text>
              <Text className="m-0 text-gray-500 text-xs">
                <a
                  href={`https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`}
                  className="text-gray-500 underline"
                >
                  Unsubscribe
                </a>
                {" | "}
                <a
                  href="https://tradecrews.com/privacy"
                  className="text-gray-500 underline"
                >
                  Privacy Policy
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

NewJobNotificationEmail.PreviewProps = {
  jobName: "New Roofing Job",
  jobId: "123",
  tradeName: "Roofing",
  budget: "1000",
  location: "San Francisco",
  deadline: "2024-12-31",
  userId: "123",
  recipientEmail: "<EMAIL>",
};

export default NewJobNotificationEmail;
