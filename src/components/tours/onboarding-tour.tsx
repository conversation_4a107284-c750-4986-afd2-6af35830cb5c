"use client";

import { type Driver, driver } from "driver.js";
import { useEffect, useState } from "react";
import "driver.js/dist/driver.css";

interface OnboardingTourProps {
  isActive: boolean;
  currentStep: "welcome" | "setup" | "complete";
  userRole: "homeowner" | "contractor";
}

export function OnboardingTour({ isActive, currentStep, userRole }: OnboardingTourProps) {
  const [driverInstance, setDriverInstance] = useState<Driver | null>(null);

  // Define tour steps for each onboarding step
  const tourSteps = {
    welcome: [
      {
        element: ".mx-auto.w-16.h-16",
        popover: {
          title: "Welcome to TradeCrews!",
          description: `You're starting your journey as a ${userRole}. This onboarding will help you get set up quickly.`,
          side: "bottom" as const,
        },
      },
      {
        element: ".grid.gap-4",
        popover: {
          title: "What You Can Do",
          description: `These are the main features available to you as a ${userRole}. We'll help you get started with each one.`,
          side: "top" as const,
        },
      },
    ],
    setup: [
      {
        element: ".text-center.space-y-2",
        popover: {
          title: "Account Setup",
          description: userRole === "homeowner" 
            ? "Now we'll add your first property so you can start creating projects."
            : "Now we'll set up your contractor profile so you can start bidding on projects.",
          side: "bottom" as const,
        },
      },
      {
        element: userRole === "homeowner" ? "button:contains('Add Property')" : ".space-y-4",
        popover: {
          title: userRole === "homeowner" ? "Add Your Property" : "Create Your Profile",
          description: userRole === "homeowner"
            ? "Click here to open the property wizard. We'll guide you through adding all the details."
            : "Fill out your contractor information to help homeowners learn about your business.",
          side: "top" as const,
        },
      },
    ],
    complete: [
      {
        element: ".mx-auto.w-16.h-16",
        popover: {
          title: "Congratulations!",
          description: "Your account is now fully set up and ready to use. You can access all features from your dashboard.",
          side: "bottom" as const,
        },
      },
      {
        element: "button:contains('Go to Dashboard')",
        popover: {
          title: "Go to Your Dashboard",
          description: "Click here to access your personalized dashboard where you can manage everything.",
          side: "top" as const,
        },
      },
    ],
  };

  useEffect(() => {
    if (!isActive || !currentStep) return;

    // Clean up previous instance
    if (driverInstance) {
      driverInstance.destroy();
    }

    const steps = tourSteps[currentStep];
    if (!steps || steps.length === 0) return;

    const instance = driver({
      showProgress: true,
      steps,
      onDestroyed: () => {
        // Tour completed or dismissed
      },
    });

    setDriverInstance(instance);

    // Start the tour after a short delay to ensure all elements are loaded
    const timer = setTimeout(() => {
      instance.drive();
    }, 1000);

    return () => {
      clearTimeout(timer);
    };
  }, [isActive, currentStep, userRole]);

  // Clean up when component unmounts
  useEffect(() => {
    return () => {
      if (driverInstance) {
        driverInstance.destroy();
      }
    };
  }, [driverInstance]);

  return null;
}