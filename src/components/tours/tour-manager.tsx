"use client";

import { useLocalStorage } from "@/hooks/use-local-storage";
import { Button } from "../ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { HelpCircle, RotateCcw } from "lucide-react";

interface TourManagerProps {
  className?: string;
}

export function TourManager({ className }: TourManagerProps) {
  const [hasSeenJobWizardTour, setHasSeenJobWizardTour] = useLocalStorage(
    "seen-enhanced-job-wizard-tour",
    false,
  );
  const [hasSeenPropertyWizardTour, setHasSeenPropertyWizardTour] = useLocalStorage(
    "seen-property-wizard-tour",
    false,
  );
  const [hasSeenProjectTour, setHasSeenProjectTour] = useLocalStorage(
    "seen-project-tour",
    false,
  );
  const [hasSeenBidTour, setHasSeenBidTour] = useLocalStorage(
    "seen-bid-tour",
    false,
  );
  const [hasSeenBidFlowTour, setHasSeenBidFlowTour] = useLocalStorage(
    "seen-bid-flow-tour",
    false,
  );

  const resetAllTours = () => {
    setHasSeenJobWizardTour(false);
    setHasSeenPropertyWizardTour(false);
    setHasSeenProjectTour(false);
    setHasSeenBidTour(false);
    setHasSeenBidFlowTour(false);
  };

  const resetSpecificTour = (tourName: string) => {
    switch (tourName) {
      case "job-wizard":
        setHasSeenJobWizardTour(false);
        break;
      case "property-wizard":
        setHasSeenPropertyWizardTour(false);
        break;
      case "project":
        setHasSeenProjectTour(false);
        break;
      case "bid":
        setHasSeenBidTour(false);
        break;
      case "bid-flow":
        setHasSeenBidFlowTour(false);
        break;
    }
  };

  const tours = [
    {
      name: "job-wizard",
      title: "Project Creation Wizard",
      description: "Learn how to create projects step-by-step",
      completed: hasSeenJobWizardTour,
    },
    {
      name: "property-wizard",
      title: "Property Creation Wizard", 
      description: "Learn how to add properties step-by-step",
      completed: hasSeenPropertyWizardTour,
    },
    {
      name: "project",
      title: "Project Creation Form",
      description: "Learn about the project creation form",
      completed: hasSeenProjectTour,
    },
    {
      name: "bid",
      title: "Bid Creation",
      description: "Learn how to create and submit bids",
      completed: hasSeenBidTour,
    },
    {
      name: "bid-flow",
      title: "Bid Review Flow",
      description: "Learn how to review and accept bids",
      completed: hasSeenBidFlowTour,
    },
  ];

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <HelpCircle className="h-5 w-5" />
          Guided Tours
        </CardTitle>
        <CardDescription>
          Reset tours to see the guided walkthroughs again
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          {tours.map((tour) => (
            <div key={tour.name} className="flex items-center justify-between p-2 border rounded">
              <div>
                <p className="font-medium text-sm">{tour.title}</p>
                <p className="text-xs text-muted-foreground">{tour.description}</p>
              </div>
              <div className="flex items-center gap-2">
                <span className={`text-xs px-2 py-1 rounded ${
                  tour.completed 
                    ? "bg-green-100 text-green-700" 
                    : "bg-gray-100 text-gray-700"
                }`}>
                  {tour.completed ? "Completed" : "Available"}
                </span>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => resetSpecificTour(tour.name)}
                  className="h-8 px-2"
                >
                  <RotateCcw className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        <Button
          onClick={resetAllTours}
          variant="outline"
          className="w-full"
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          Reset All Tours
        </Button>
      </CardContent>
    </Card>
  );
}