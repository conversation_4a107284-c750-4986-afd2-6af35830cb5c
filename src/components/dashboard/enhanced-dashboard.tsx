"use client";

import { useQuery } from "@tanstack/react-query";
import { ClockIcon, FileTextIcon } from "lucide-react";
import { useState } from "react";
import { ContractorDashboard } from "@/components/contractor/contractor-dashboard";
import { HomeownerDashboard } from "@/components/homeowner/homeowner-dashboard";
import { useTRPC } from "@/components/trpc/client";
import { useSession } from "@/lib/auth-client";
import {
  ActionableStats,
  generateContractorStats,
  generateHomeownerStats,
} from "./actionable-stats";
import { DashboardLoadingSkeleton } from "./dashboard-loading-skeleton";
import { MobileQuickStats } from "./mobile-dashboard";
import { alertsToNotifications, NotificationsMenu } from "./notifications-menu";
import { PriorityCards } from "./priority-cards";
import {
  contractorActionConfig,
  homeownerActionConfig,
  QuickActionsMenu,
} from "./quick-actions-menu";
import {
  generateContractorAlerts,
  generateHomeownerAlerts,
} from "./smart-alerts";

export function EnhancedDashboard() {
  const trpc = useTRPC();
  const { data: session, isPending: isSessionLoading } = useSession();
  const isProfessional = session?.user?.role === "contractor";

  // Fetch enhanced dashboard data
  const { data: contractorDashboardStats } = useQuery(
    trpc.dashboard.getContractorDashboardStats.queryOptions(undefined, {
      enabled: isProfessional,
    }),
  );

  const { data: homeownerDashboardStats } = useQuery(
    trpc.dashboard.getHomeownerDashboardStats.queryOptions(undefined, {
      enabled: !isProfessional,
    }),
  );

  // Fetch jobs with approaching deadlines for contractors
  const { data: jobsWithDeadlines } = useQuery(
    trpc.dashboard.getJobsWithDeadlines.queryOptions(
      { hoursAhead: 24 },
      { enabled: isProfessional },
    ),
  );

  // Fetch pending bids for homeowners
  const { data: pendingBidsData } = useQuery(
    trpc.dashboard.getPendingBidsForUser.queryOptions(undefined, {
      enabled: !isProfessional,
    }),
  );

  // Fetch unread message count
  const { data: unreadMessageCount } = useQuery(
    trpc.dashboard.getUnreadMessageCount.queryOptions(),
  );

  // Generate smart alerts based on real data
  const alerts = isProfessional
    ? generateContractorAlerts({
        newJobs: jobsWithDeadlines?.length || 0,
        bidDeadlines:
          jobsWithDeadlines?.filter((job) => {
            const hoursUntilDeadline =
              (new Date(job.job.deadline).getTime() - Date.now()) /
              (1000 * 60 * 60);
            return hoursUntilDeadline <= 6;
          }).length || 0,
        acceptedBids: contractorDashboardStats?.acceptedBids || 0,
        unreadMessages: unreadMessageCount || 0,
      })
    : generateHomeownerAlerts({
        pendingBids: pendingBidsData?.length || 0,
        expiringSoon: 0, // Homeowners don't have bid deadlines
        completedJobs: homeownerDashboardStats?.completedJobs || 0,
        unreadMessages: unreadMessageCount || 0,
      });

  // Generate actionable stats with real data
  const actionableStats = isProfessional
    ? generateContractorStats({
        totalBids: contractorDashboardStats?.totalBids || 0,
        activeJobs: contractorDashboardStats?.activeJobs || 0,
        completedJobs: contractorDashboardStats?.completedJobs || 0,
        winRate: contractorDashboardStats?.winRate || 0,
        avgResponseTime: 18, // This would come from bid response time analysis
      })
    : generateHomeownerStats({
        totalJobs: homeownerDashboardStats?.totalJobs || 0,
        activeJobs: homeownerDashboardStats?.activeJobs || 0,
        completedJobs: homeownerDashboardStats?.completedJobs || 0,
        pendingBids: pendingBidsData?.length || 0,
        avgResponseTime: 24, // This would come from contractor response analysis
      });

  // Mobile quick stats with real data
  const mobileStats = isProfessional
    ? [
        {
          label: "Active Jobs",
          value: contractorDashboardStats?.activeJobs || 0,
          trend: "neutral" as const,
        },
        {
          label: "Win Rate",
          value: `${contractorDashboardStats?.winRate || 0}%`,
          trend:
            (contractorDashboardStats?.winRate || 0) > 25
              ? ("up" as const)
              : (contractorDashboardStats?.winRate || 0) < 15
                ? ("down" as const)
                : ("neutral" as const),
          change:
            (contractorDashboardStats?.winRate || 0) > 25
              ? "+5%"
              : (contractorDashboardStats?.winRate || 0) < 15
                ? "-5%"
                : undefined,
        },
        {
          label: "Pending",
          value: contractorDashboardStats?.proposedBids || 0,
          trend:
            (contractorDashboardStats?.proposedBids || 0) > 3
              ? ("up" as const)
              : ("neutral" as const),
        },
      ]
    : [
        {
          label: "Active",
          value: homeownerDashboardStats?.activeJobs || 0,
          trend: "neutral" as const,
        },
        {
          label: "Pending",
          value: pendingBidsData?.length || 0,
          trend:
            (pendingBidsData?.length || 0) > 0
              ? ("up" as const)
              : ("neutral" as const),
        },
        {
          label: "Complete",
          value: homeownerDashboardStats?.completedJobs || 0,
          trend: "up" as const,
        },
      ];

  // Priority items for quick access
  const urgentDeadlines =
    jobsWithDeadlines?.filter((job) => {
      const hoursUntilDeadline =
        (new Date(job.job.deadline).getTime() - Date.now()) / (1000 * 60 * 60);
      return hoursUntilDeadline <= 6;
    }) || [];

  const priorityItems = isProfessional
    ? urgentDeadlines.length > 0
      ? [
          {
            id: "urgent-bids",
            title: "Bid Deadlines Today",
            description: `${urgentDeadlines.length} job${urgentDeadlines.length > 1 ? "s" : ""} close for bidding soon`,
            urgency: "high" as const,
            action: { label: "Submit Bids", href: "/projects" },
            icon: <ClockIcon className="h-4 w-4" />,
            value: urgentDeadlines.length.toString(),
          },
        ]
      : []
    : (pendingBidsData?.length || 0) > 0
      ? [
          {
            id: "pending-review",
            title: "Bids to Review",
            description: `${pendingBidsData?.length} new bid${(pendingBidsData?.length || 0) > 1 ? "s" : ""} received`,
            urgency: "high" as const,
            action: { label: "Review", href: "/projects" },
            icon: <FileTextIcon className="h-4 w-4" />,
            value: (pendingBidsData?.length || 0).toString(),
          },
        ]
      : [];

  // Convert alerts to notifications
  const [dismissedNotifications, setDismissedNotifications] = useState<
    Set<string>
  >(new Set());
  const notifications = alertsToNotifications(alerts).filter(
    (notification) => !dismissedNotifications.has(notification.id),
  );

  const handleMarkAsRead = (notificationId: string) => {
    // In a real app, this would update the backend
    console.log("Marked as read:", notificationId);
  };

  const handleMarkAllAsRead = () => {
    // In a real app, this would update the backend
    console.log("Marked all as read");
  };

  const handleDismiss = (notificationId: string) => {
    setDismissedNotifications((prev) => new Set([...prev, notificationId]));
  };

  // Show loading skeleton while session is loading to prevent layout shift
  if (isSessionLoading) {
    return (
      <div className="space-y-6">
        {/* Header skeleton */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="h-9 w-32 animate-pulse rounded-md bg-muted" />
          </div>
          <div className="h-9 w-9 animate-pulse rounded-md bg-muted" />
        </div>

        {/* Stats skeleton */}
        <div className="space-y-4">
          <div className="sm:hidden">
            <div className="grid grid-cols-3 gap-4">
              {Array.from({ length: 3 }).map((_, i) => (
                // biome-ignore lint/suspicious/noArrayIndexKey: Static skeleton items
                <div key={i} className="animate-pulse rounded-lg bg-muted p-4">
                  <div className="mb-2 h-4 w-16 rounded bg-background" />
                  <div className="h-6 w-12 rounded bg-background" />
                </div>
              ))}
            </div>
          </div>
          <div className="hidden sm:block">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {Array.from({ length: 4 }).map((_, i) => (
                // biome-ignore lint/suspicious/noArrayIndexKey: Static skeleton items
                <div key={i} className="animate-pulse rounded-lg bg-muted p-6">
                  <div className="mb-2 h-4 w-24 rounded bg-background" />
                  <div className="mb-2 h-8 w-16 rounded bg-background" />
                  <div className="h-3 w-32 rounded bg-background" />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Dashboard content skeleton */}
        <DashboardLoadingSkeleton />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with quick actions and notifications */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <QuickActionsMenu
            actions={[
              ...(isProfessional
                ? contractorActionConfig.primary
                : homeownerActionConfig.primary),
              ...(isProfessional
                ? contractorActionConfig.secondary.map((action) =>
                    action.id === "messages"
                      ? {
                          ...action,
                          badge: unreadMessageCount
                            ? unreadMessageCount.toString()
                            : undefined,
                        }
                      : action,
                  )
                : homeownerActionConfig.secondary.map((action) =>
                    action.id === "messages"
                      ? {
                          ...action,
                          badge: unreadMessageCount
                            ? unreadMessageCount.toString()
                            : undefined,
                        }
                      : action,
                  )),
            ]}
            primaryActions={
              isProfessional
                ? contractorActionConfig.primary
                : homeownerActionConfig.primary
            }
            title="Quick Actions"
          />
        </div>
        <NotificationsMenu
          notifications={notifications}
          onMarkAsRead={handleMarkAsRead}
          onMarkAllAsRead={handleMarkAllAsRead}
          onDismiss={handleDismiss}
        />
      </div>

      {/* Stats overview */}
      <div className="space-y-4">
        {/* Mobile: Compact stats */}
        <div className="sm:hidden">
          <MobileQuickStats stats={mobileStats} />
        </div>
        {/* Desktop: Full actionable stats */}
        <div className="hidden sm:block">
          <ActionableStats stats={actionableStats} />
        </div>
      </div>

      {/* Priority items */}
      {priorityItems.length > 0 && (
        <PriorityCards items={priorityItems} title="Needs Attention" />
      )}

      {/* Original dashboard content */}
      {isProfessional ? <ContractorDashboard /> : <HomeownerDashboard />}
    </div>
  );
}
