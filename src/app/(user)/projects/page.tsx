import { headers } from "next/headers";
import Link from "next/link";
import { JobCard } from "@/components/job/job-card";
import { ProfessionalJobList } from "@/components/job/professional-job-list";
import { PageLayout } from "@/components/page-layout";
import { getQueryClient, trpc } from "@/components/trpc/server";
import { buttonVariants } from "@/components/ui/button";
import { auth } from "@/lib/auth";

export default async function JobsPage() {
  const session = await auth.api.getSession({ headers: await headers() });
  const isProfessional = session?.user?.role === "contractor";

  const queryClient = getQueryClient();
  const jobs = isProfessional
    ? null
    : await queryClient.fetchQuery(trpc.projects.listForUser.queryOptions());

  const actions = !isProfessional ? (
    <Link
      href="/projects/new"
      className={buttonVariants({ variant: "default" })}
    >
      New Project
    </Link>
  ) : null;

  return (
    <PageLayout title="Projects" actions={actions}>
      {isProfessional ? (
        <ProfessionalJobList />
      ) : (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3 xl:grid-cols-4">
          {jobs?.map((job) => <JobCard key={job.id} job={job} />)}
        </div>
      )}
    </PageLayout>
  );
}
