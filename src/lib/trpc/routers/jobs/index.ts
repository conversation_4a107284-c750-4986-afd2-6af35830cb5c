import { router } from "@/lib/trpc/procedures";
import { completionRouter } from "./completion";
import { coreProjectsRouter } from "./core";
import { creationRouter } from "./creation";
import { listingRouter } from "./listing";
import { quickHireRouter } from "./quickhire";
import { scheduleRouter } from "./schedule";

export const projectsRouter = router({
  ...completionRouter,
  ...coreProjectsRouter,
  ...creationRouter,
  ...listingRouter,
  ...quickHireRouter,
  ...scheduleRouter,
});
