import { TRPCError } from "@trpc/server";
import { and, eq, type InferInsertModel, inArray } from "drizzle-orm";
import { z } from "zod/v4";
import { db } from "@/db";
import {
  account,
  job,
  jobImage,
  jobTemplate,
  organization,
  task,
  trade,
} from "@/db/schema";
import { sendJobNotificationEmails } from "@/lib/email/send-job-notification";
import { jobProcedures } from "@/lib/trpc/procedures";

export const creationRouter = {
  create: jobProcedures.create
    .input(
      z.object({
        name: z.string(),
        budget: z.number(),
        tasks: z
          .object({ name: z.string(), tradeId: z.string() })
          .array()
          .refine((tasks) => tasks.length === 1 || tasks.length > 0, {
            message: "Quick hire jobs must have exactly one task",
            path: ["tasks"],
          }),
        propertyId: z.string(),
        startsAt: z.date(),
        deadline: z.date(),
        jobType: z.enum(["STANDARD", "QUICK_HIRE"]).default("STANDARD"),
        isRecurring: z.boolean().default(false),
        recurringFrequency: z.string().nullable(),
        images: z
          .object({
            url: z.string().optional(),
            description: z.string().optional().nullable(),
          })
          .array()
          .optional(),
        templateId: z.string().optional(),
      }),
    )
    .mutation(async ({ input }) => {
      // For quick hire jobs, validate that all trades are eligible and there's exactly one task
      if (input.jobType === "QUICK_HIRE") {
        if (input.tasks.length !== 1) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Quick hire jobs must have exactly one task",
          });
        }

        const tradeIds = input.tasks.map((task) => task.tradeId);

        const eligibleTrades = await db.query.trade.findMany({
          where: and(
            inArray(trade.id, tradeIds),
            eq(trade.availableForQuickHire, true),
          ),
        });

        if (eligibleTrades.length !== tradeIds.length) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "One or more selected trades are not available for quick hire",
          });
        }
      }

      const data: InferInsertModel<typeof job> = {
        name: input.name,
        budget: input.budget,
        propertyId: input.propertyId,
        startsAt: input.startsAt,
        deadline: input.deadline,
        jobType: input.jobType,
        isRecurring: input.isRecurring,
        recurringFrequency: input.recurringFrequency,
      };

      // Create the job
      const [newJob] = await db.insert(job).values(data).returning();

      if (!newJob) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create job",
        });
      }

      // Create tasks
      await db.insert(task).values(
        input.tasks.map((task) => ({
          name: task.name,
          tradeId: task.tradeId,
          jobId: newJob.id,
        })),
      );

      // Add images if they exist
      if (input.images && input.images.length > 0) {
        await db.insert(jobImage).values(
          input.images.map((image) => ({
            jobId: newJob.id,
            url: image.url as string,
            description: image.description,
          })),
        );
      }

      // Send push notifications to contractors in the relevant trades
      if (newJob.status === "PUBLISHED") {
        const tradeIds = input.tasks.map((task) => task.tradeId);

        // Send email notifications
        await sendJobNotificationEmails(newJob.id, tradeIds);

        // Find organizations with matching trades
        const orgs = await db.query.organization.findMany({
          where: inArray(organization.tradeId, tradeIds),
          with: {
            memberships: {
              columns: {
                userId: true,
              },
            },
          },
        });

        // Get all account IDs associated with these organizations
        const userIds = orgs.flatMap((org) =>
          org.memberships.map((m) => m.userId),
        );

        const accountIds = await db.query.account.findMany({
          where: inArray(account.userId, userIds),
          columns: {
            id: true,
          },
        });

        // Send push notification to each account
        for (const account of accountIds) {
          try {
            // Dynamically import web-push only on server side
            const webpush = await import("web-push");
            await webpush.default.sendNotification(
              {
                endpoint: account.id,
                keys: { p256dh: "", auth: "" }, // These will be replaced in the API
              },
              JSON.stringify({
                title: "New Job Available",
                body: `A new job "${newJob.name}" has been posted in your trade area.`,
                data: {
                  jobId: newJob.id,
                  url: `/jobs/${newJob.id}`,
                },
              }),
            );
          } catch (error) {
            console.error(
              `Failed to send notification to account ${account.id}:`,
              error,
            );
          }
        }
      }

      return newJob;
    }),

  createFromTemplate: jobProcedures.create
    .input(
      z.object({
        templateId: z.string(),
        propertyId: z.string(),
        startsAt: z.date(),
        deadline: z.date(),
        images: z
          .object({ url: z.string(), description: z.string() })
          .array()
          .optional(),
      }),
    )
    .mutation(async ({ input }) => {
      // Get the template
      const template = await db.query.jobTemplate.findFirst({
        where: eq(jobTemplate.id, input.templateId),
        with: { tasks: true },
      });

      if (!template) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Template not found",
        });
      }

      // Create job from template
      const [newJob] = await db
        .insert(job)
        .values({
          name: template.name,
          budget: template.budget,
          propertyId: input.propertyId,
          startsAt: input.startsAt,
          deadline: input.deadline,
        })
        .returning();

      if (!newJob) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create job",
        });
      }

      // Create tasks
      await db.insert(task).values(
        template.tasks.map((t) => ({
          name: t.name,
          tradeId: t.tradeId,
          jobId: newJob.id,
        })),
      );

      // Add images if they exist
      if (input.images && input.images.length > 0) {
        await db.insert(jobImage).values(
          input.images.map((image) => ({
            jobId: newJob.id,
            url: image.url,
            description: image.description,
          })),
        );
      }

      return newJob;
    }),
};
